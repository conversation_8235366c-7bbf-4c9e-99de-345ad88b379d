import { Toaster } from "@/components/ui/toaster";
import { ReactHotToaster } from "@/components/ui/react-hot-toast";
import { TooltipProvider } from "@/components/ui/tooltip";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { BrowserRouter, Routes, Route, Navigate } from "react-router-dom";
import { useAuth } from "@/hooks/auth/useAuth";
import { useState, useEffect } from "react";

import SplashScreen2 from "@/components/ui/SplashScreen2";
import { PWAInstallPrompt } from "@/components/pwa/PWAInstallPrompt";
import { PWAUpdateNotification } from "@/components/pwa/PWAUpdateNotification";





import CreateDeal from "./pages/deals/CreateDeal";
import ConfirmBooking from "./pages/bookings/ConfirmBooking";
import BookingDetails from "./pages/bookings/BookingDetails";
import BookingConfirmed from "./pages/bookings/BookingConfirmed";

import BusinessDeals from "./pages/BusinessDeals";
import EditDeal from "./pages/deals/EditDeal";
import BusinessBookings from "./pages/bookings/BusinessBookings";
import BusinessBookingDetails from "./pages/bookings/BusinessBookingDetails";
import { ABTestProvider } from "@/contexts/ABTestContext";
import ThemeProvider from "@/components/ThemeProvider";
import ABTestTracker from "@/components/ABTestTracker";

import AssistantSelect from "./pages/aiassistant/AssistantSelect";

import Conversations from "./pages/chatroom/Conversations";
import ConversationChatDetails from "./pages/chatroom/ConversationChatDetails";
import MapNew from "./pages/MapNew";
import AdminDataPage from "./pages/admin/AdminDataPage";
import ABTestingPage from "./pages/admin/ABTestingPage";
import BusinessDashboard from "./pages/BusinessView";
import BusinessView from "./pages/BusinessView";
import UserDealDetails from "./pages/deals/UserDealDetails";
import UserBusinessView from "./pages/business/UserBusinessView";

import AvailabilityTest from "./pages/test/AvailabilityTest";
import ConversationTest from "./pages/test/ConversationTest";
import PushNotificationTest from "./pages/test/PushNotificationTest";
import VoiceNavigationTest from "./pages/test/VoiceNavigationTest";

import { DealsListingPage } from "./pages/deals/DealsListingPage";
import { DealDetailPage } from "./pages/deals/DealDetailPage";
import { BookingPage } from "./pages/bookings/BookingPage";
import Onboarding from "./pages/Onboarding";
import LoginRequired from "./pages/auth/LoginRequired";
import PersonalizePreferences from "./pages/auth/PersonalizePreferences";

import { LocationProvider } from "@/contexts/LocationContext";
import { useUserPreferencesSetup } from "@/hooks/useUserPreferencesSetup";
import { useLocationTracking } from "@/hooks/location/useLocationTracking";

import LangGraphTest from "./pages/test/LangGraphTest";
import CatchUpAgentChat from "./pages/CatchUpAgentChat";
import Groups from "./pages/groups/Groups";
import GroupDetails from "./pages/groups/GroupDetails";

import { cacheManager } from "@/utils/cacheManager";

{/* Voice Dialog Context and Component */}
import { VoiceDialogProvider } from "@/contexts/VoiceDialogContext";
import GlobalVoiceDialog from "@/features/voiceai/ui/GlobalVoiceDialog";

{/* Menu pages */}
import HomeUser from "./pages/mains/HomeUser";
import Deals from "./pages/mains/Deals";
import MyBookings from "./pages/mains/MyBookings";
import Profile from "./pages/mains/Profile";
import Experience from "./pages/mains/Experience";
import Social from "./pages/mains/Social";
import Meteo from "./pages/mains/Meteo";
import Email from "./pages/mains/Email";
import CalendarPage from "./pages/mains/Calendar";
import { Layout } from "./pages/layouts/LayoutUser";
import NotFound from "./pages/NotFound";
import DashboardBusiness from "./pages/DashboardBusiness";
import SubscriptionPage from "./pages/business/SubscriptionPage";
import LocationTrackingPage from "./pages/admin/LocationTrackingPage";

import Login from "./pages/auth/Login";
import Signup from "./pages/auth/Signup";

const AppRoutes = () => {
  const { isAuthenticated } = useAuth();
  
  // Automatically setup user preferences when they access the application
  useUserPreferencesSetup();
  
  // Automatically track location every minute for authenticated users
  useLocationTracking();

  if (isAuthenticated === null) {
    return null;
  }

  return (
    <Routes>
        {/* Public routes that don't require authentication */}
        <Route path="/" element={<HomeUser />} />
        <Route path="/profile" element={<Profile />} />
        <Route path="/deals" element={<Deals />} />
        <Route path="/deals/:id" element={<DealDetailPage />} />
        <Route path="/map" element={<MapNew />} />
        
        {/* Tab routes with layout */}
        {/* <Route path="/experiences" element={<Layout  title="Esperienze" />}>
          <Route index element={<Experience />} />
        </Route> */}
        <Route path="/social" element={<Layout title="Social" />}>
          <Route index element={<Social />} />
        </Route>
        {/* <Route path="/ar" element={<Layout title="AR Deals" />}>
          <Route index element={<div className="p-8 text-center"><h2>AR Deals Coming Soon</h2></div>} />
        </Route> */}
        <Route path="/meteo" element={<Layout  title="Meteo" />}>
          <Route index element={<Meteo />} />
        </Route>
        {/* <Route path="/eventi" element={<Layout title="Eventi" />}>
          <Route index element={<div className="p-8 text-center"><h2>Eventi Coming Soon</h2></div>} />
        </Route>
        <Route path="/email" element={<Layout title="Email" />}>
          <Route index element={<Email />} />
        </Route>
        <Route path="/calendar" element={<Layout title="Calendario" />}>
          <Route index element={<CalendarPage />} />
        </Route> */}
        
   

        <Route path="/deal/:id" element={<UserDealDetails />} />
        <Route path="/onboarding" element={<Onboarding />} />
        <Route path="/business/:id" element={<BusinessView />} />
        <Route path="/business-view/:businessId" element={<UserBusinessView />} />

        {/* Login & SignUp routes */}
        <Route
          path="/login"
          element={isAuthenticated ? <Navigate to="/" /> : <Login />}
        />
        <Route
          path="/signup"
          element={isAuthenticated ? <Navigate to="/" /> : <Signup />}
        />
        <Route
          path="/personalize-preferences"
          element={
            isAuthenticated ? (
              <PersonalizePreferences />
            ) : (
              <Navigate to="/login" />
            )
          }
        />

        {/* Chat routes */}
        <Route
          path="/conversations"
          element={isAuthenticated ? <Conversations /> : <LoginRequired />}
        />
        <Route
          path="/conversations/:conversationId"
          element={
            isAuthenticated ? <ConversationChatDetails /> : <LoginRequired />
          }
        />
        <Route
          path="/chat-catchup"  // OpenaAI RealTime
          element={
            isAuthenticated ? <CatchUpAgentChat /> : <Navigate to="/login" />
          }
        />
        <Route path="/conversation-test" element={<ConversationTest />} />
        <Route path="/voice-navigation-test" element={<VoiceNavigationTest />} />
        <Route path="/langgraph" element={<LangGraphTest />} />
  


        {/* Protected routes that require authentication for booking */}
        <Route
          path="/book/:id"
          element={isAuthenticated ? <BookingPage /> : <Navigate to="/login" />}
        />
        <Route
          path="/confirm-booking"
          element={
            isAuthenticated ? <ConfirmBooking /> : <Navigate to="/login" />
          }
        />

        {/* Other protected routes */}
        <Route
          path="/subscription"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <SubscriptionPage />
            )
          }
        />
        <Route
          path="/business/:businessId/create-deal"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <CreateDeal />
            )
          }
        />
        <Route
          path="/business-dashboard"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <DashboardBusiness />
            )
          }
        />
        <Route
          path="/business/:id"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <BusinessView />
            )
          }
        />
        <Route
          path="/business/:id/bookings"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <BusinessBookings />
            )
          }
        />
        <Route
          path="/business/:id/bookings/:bookingId"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <BusinessBookingDetails />
            )
          }
        />
        <Route
          path="/business/:id/deals"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <BusinessDeals />
            )
          }
        />
        <Route
          path="/admin-data"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <AdminDataPage />
            )
          }
        />
        <Route
          path="/admin/location-tracking"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <LocationTrackingPage />
            )
          }
        />
        <Route
          path="/admin/ab-testing"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <ABTestingPage />
            )
          }
        />
        <Route
          path="/mybookings"
          element={isAuthenticated ? <MyBookings /> : <LoginRequired />}
        />
        <Route
          path="/booking-confirmed/:id"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <BookingConfirmed />
            )
          }
        />
        <Route
          path="/prenotazione/:id"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <BookingDetails />
            )
          }
        />
        <Route
          path="/deal/:dealId/edit"
          element={
            isAuthenticated === false ? <Navigate to="/login" /> : <EditDeal />
          }
        />
        <Route
          path="/assistant-select"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <AssistantSelect />
            )
          }
        />
      
      

        <Route
          path="/groups"
          element={
            isAuthenticated === false ? <Navigate to="/login" /> : <Groups />
          }
        />
        <Route
          path="/groups/:id"
          element={
            isAuthenticated === false ? <Navigate to="/login" /> : <GroupDetails />
          }
        />

        <Route path="/deals/:id" element={<DealDetailPage />} />
        <Route path="/book/:id" element={<BookingPage />} />
        <Route path="/push-notification-test" element={<PushNotificationTest />} />
        <Route path="*" element={<NotFound />} />
        {/* test pages */}
        <Route
          path="/availabilitytest"
          element={
            isAuthenticated === false ? (
              <Navigate to="/login" />
            ) : (
              <AvailabilityTest />
            )
          }
        />
       
    


      </Routes>
  );
};

const App = () => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: 1,
        refetchOnWindowFocus: false,
      },
    },
  });

  const [showSplash, setShowSplash] = useState(true);
  

  // Handle splash screen
  useEffect(() => {
    const hasVisitedBefore = sessionStorage.getItem("hasVisited");

    if (hasVisitedBefore) {
      setShowSplash(false);
    } else {
      sessionStorage.setItem("hasVisited", "true");
      setShowSplash(true);
    }
  }, []);

  // Initialize cache management
  useEffect(() => {
    const initializeCacheManagement = async () => {
      try {
        // Clean up expired caches on app start
        await cacheManager.cleanupExpiredCaches();
        
        // Preload critical resources
        await cacheManager.preloadCriticalResources();
        
        // Set current app version
        await cacheManager.setCurrentVersion('1.0.0');
        
        console.log('Cache management initialized');
      } catch (error) {
        console.error('Error initializing cache management:', error);
      }
    };

    initializeCacheManagement();
  }, []);

  const handleSplashFinish = () => {
    setShowSplash(false);
  };

  return (
    <QueryClientProvider client={queryClient}>
      <ABTestProvider>
        <ThemeProvider>
          <VoiceDialogProvider>
            <TooltipProvider>
              <Toaster />
              <ReactHotToaster
                position="top-center"
                duration={4000}
                maxToasts={5}
                toastOptions={{
                  success: {
                    duration: 3000,
                  },
                  error: {
                    duration: 5000,
                  },
                }}
              />

              {showSplash ? (
                <SplashScreen2 onFinish={handleSplashFinish} />
              ) : (
                <BrowserRouter>
                  <LocationProvider>
                    <ABTestTracker />
                    <PWAInstallPrompt variant="banner" />
                    <PWAUpdateNotification />
                    <AppRoutes />
                    <GlobalVoiceDialog />
                  </LocationProvider>
                </BrowserRouter>
              )}
            </TooltipProvider>
          </VoiceDialogProvider>
        </ThemeProvider>
      </ABTestProvider>
    </QueryClientProvider>
  );
};

export default App;
