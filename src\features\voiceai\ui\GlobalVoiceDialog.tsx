import React from 'react';
import VoiceDialog, { RenderMode } from '@/features/voiceai/ui/VoiceDialog';
import { useVoiceDialog } from '@/contexts/VoiceDialogContext';



interface GlobalVoiceDialogProps {
  renderMode?: RenderMode;
}

const GlobalVoiceDialog: React.FC<GlobalVoiceDialogProps> = ({
  renderMode = "conversation"
}) => {
  const { isVoiceDialogOpen, closeVoiceDialog, businessId } = useVoiceDialog();

  return (
    <VoiceDialog
      isOpen={isVoiceDialogOpen}
      onClose={closeVoiceDialog}
      renderMode={renderMode}
      businessId={businessId}
    />
  );
};

export default GlobalVoiceDialog;
