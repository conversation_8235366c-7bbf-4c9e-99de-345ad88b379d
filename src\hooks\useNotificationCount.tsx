
import { useEffect } from 'react';
import { useQueryClient } from '@tanstack/react-query';
import { supabase } from '@/integrations/supabase/client';
import { useGetAllNotifications } from './notifications/useNotifications';

export const useNotificationCount = () => {
  const queryClient = useQueryClient();
  const { data: notifications = [] } = useGetAllNotifications();
  
  // Real-time subscription per la tabella notifications
  useEffect(() => {
    const channel = supabase
      .channel('notifications-realtime')
      .on(
        'postgres_changes',
        {
          event: '*',
          schema: 'public',
          table: 'notifications'
        },
        (payload) => {
          console.log('Real-time notification update:', payload);
          // Invalidate and refetch the query when any change occurs
          queryClient.invalidateQueries({ queryKey: ['notifications'] });
        }
      )
      .subscribe();

    return () => {
      supabase.removeChannel(channel);
    };
  }, [queryClient]);
  
  // Contiamo le notifiche dalla tabella notifications
  const totalCount = notifications.length;
  
  return {
    totalCount,
    notifications,
    hasNotifications: totalCount > 0
  };
};
